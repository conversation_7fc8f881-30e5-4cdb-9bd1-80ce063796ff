project_name: vint-release
run_name: vint-5c

# training setup
use_mlflow: True # set to false if you don't want to log to mlflow
train: True
batch_size: 256
epochs: 100
gpu_ids: [0]
num_workers: 12
lr: 5e-4
optimizer: adamw
clipping: False
max_norm: 1.
scheduler: "cosine"
warmup: True
warmup_epochs: 4
cyclic_period: 10
plateau_patience: 3
plateau_factor: 0.5
seed: 0

# model params
model_type: vint
obs_encoder: "efficientnet-b0" # by default, this is imagenet pretrained
obs_encoding_size: 512
mha_num_attention_heads: 4
mha_num_attention_layers: 4
mha_ff_dim_factor: 4
late_fusion: False

# normalization for the action space
normalize: True

# context
context_type: temporal
context_size: 5
# tradeoff between action and distance prediction loss
alpha: 0.5

# distance bounds for distance and action and distance predictions
distance:
  min_dist_cat: 0
  max_dist_cat: 20
action:
  min_dist_cat: 0
  max_dist_cat: 10
close_far_threshold: 10 # distance threshold used to seperate the close and the far  subgoals that are sampled per datapoint

# action output params
len_traj_pred: 5
learn_angle: True

# dataset specific parameters
image_size: [85, 64] # width, height
goal_type: "image"

# logging stuff
## =0 turns off
print_log_freq: 100 # in iterations
image_log_freq: 1000 #0 # in iterations
num_images_log: 8 #0
pairwise_test_freq: 0 # in epochs
eval_fraction: 0.25
mlflow_log_freq: 10 # in iterations
eval_freq: 1 # in epochs
