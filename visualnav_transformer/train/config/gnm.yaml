# NOTE: this model uses private datasets

project_name: vint-release
run_name: gnm

# training setup
use_mlflow: True # set to false if you don't want to log to mlflow
train: True
batch_size: 400
eval_batch_size: 400
epochs: 30
gpu_ids: [0]
num_workers: 4
lr: 7e-4
optimizer: adam
seed: 0

# model params
model_type: gnm
obs_encoding_size: 1024
goal_encoding_size: 1024

# normalization for the action space
normalize: True

# context
context_type: temporal # [temporal, randomized]
context_size: 5

# tradeoff between action and distance prediction loss
alpha: 0.5

# distance bounds for distance and action and distance predictions
distance:
  min_dist_cat: 0
  max_dist_cat: 20
action:
  min_dist_cat: 2
  max_dist_cat: 10
close_far_threshold: 10 # distance threshold used to seperate the close and the far  subgoals that are sampled per datapoint

# action output params
len_traj_pred: 5
learn_angle: True

# dataset specific parameters
image_size: [85, 64] # width, height
goal_type: "image"


datasets:
  recon:
    data_folder: /home/<USER>/vint_dataset/recon
    train: /home/<USER>/data_splits/recon/train/ # path to train folder with traj_names.txt
    test: /home/<USER>/data_splits/recon/test/ # path to test folder with traj_names.txt
    end_slack: 3 # because many trajectories end in collisions
    goals_per_obs: 1 # how many goals are sampled per observation
    negative_mining: True # negative mining from the ViNG paper (Shah et al.)
  go_stanford:
    data_folder: /home/<USER>/vint_dataset/go_stanford_cropped # datasets/stanford_go_new
    train: /home/<USER>/data_splits/go_stanford/train/
    test: /home/<USER>/data_splits/go_stanford/test/
    end_slack: 0
    goals_per_obs: 2 # increase dataset size
    negative_mining: True
  cory_hall:
    data_folder: /home/<USER>/vint_dataset/cory_hall/
    train: /home/<USER>/data_splits/cory_hall/train/
    test: /home/<USER>/data_splits/cory_hall/test/
    end_slack: 3 # because many trajectories end in collisions
    goals_per_obs: 1
    negative_mining: True
  tartan_drive:
    data_folder: /home/<USER>/vint_dataset/tartan_drive/
    train: /home/<USER>/data_splits/tartan_drive/train/
    test: /home/<USER>/data_splits/tartan_drive/test/
    end_slack: 3 # because many trajectories end in collisions
    goals_per_obs: 1
    negative_mining: True
  sacson:
    data_folder: /home/<USER>/vint_dataset/sacson/
    train: /home/<USER>/data_splits/sacson/train/
    test: /home/<USER>/data_splits/sacson/test/
    end_slack: 3 # because many trajectories end in collisions
    goals_per_obs: 1
    negative_mining: True

  # private datasets (uncomment if you have access)
  # seattle:
  #   data_folder: /home/<USER>/vint_dataset/seattle/
  #   train: /home/<USER>/data_splits/seattle/train/
  #   test: /home/<USER>/data_splits/seattle/test/
  #   end_slack: 0
  #   goals_per_obs: 1
  #   negative_mining: True
  # scand:
  #   data_folder: /home/<USER>/vint_dataset/scand/
  #   train: /home/<USER>/data_splits/scand/train/
  #   test: /home/<USER>/data_splits/scand/test/
  #   end_slack: 0
  #   goals_per_obs: 1
  #   negative_mining: True


# logging stuff
## =0 turns off
print_log_freq: 100 # in iterations
image_log_freq: 1000 # in iterations
num_images_log: 8 # number of images to log in a logging iteration
pairwise_test_freq: 20 # in epochs
mlflow_log_freq: 10 # in iterations
eval_freq: 1 # in epochs
