project_name: nomad
run_name: nomad

# training setup
use_mlflow: True # set to false if you don't want to log to mlflow
train: True
batch_size: 256
epochs: 100
gpu_ids: [0]
num_workers: 12
lr: 1e-4
optimizer: adamw
clipping: False
max_norm: 1.
scheduler: "cosine"
warmup: True
warmup_epochs: 4
cyclic_period: 10
plateau_patience: 3
plateau_factor: 0.5
seed: 0

# model params
model_type: nomad
vision_encoder: nomad_vint
encoding_size: 256
obs_encoder: efficientnet-b0
attn_unet: False
cond_predict_scale: False
mha_num_attention_heads: 4
mha_num_attention_layers: 4
mha_ff_dim_factor: 4
down_dims: [64, 128, 256]

# diffusion model params
num_diffusion_iters: 10

# mask
goal_mask_prob: 0.5

# normalization for the action space
normalize: True

# context
context_type: temporal
context_size: 3 # 5
alpha: 1e-4

# distance bounds for distance and action and distance predictions
distance:
  min_dist_cat: 0
  max_dist_cat: 20
action:
  min_dist_cat: 3
  max_dist_cat: 20

# action output params
len_traj_pred: 8
learn_angle: False

# dataset specific parameters
image_size: [96, 96] # width, height

print_log_freq: 100 # in iterations
image_log_freq: 1000 #0 # in iterations
num_images_log: 8 #0
pairwise_test_freq: 0 # in epochs
eval_fraction: 0.25
mlflow_log_freq: 10 # in iterations
eval_freq: 1 # in epochs
