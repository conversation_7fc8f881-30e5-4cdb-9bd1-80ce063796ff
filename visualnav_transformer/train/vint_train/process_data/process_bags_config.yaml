tartan_drive:
  odomtopics: "/odometry/filtered_odom"
  imtopics: "/multisense/left/image_rect_color"
  ang_offset: 1.5707963267948966 # pi/2
  img_process_func: "process_tartan_img"
  odom_process_func: "nav_to_xy_yaw"

scand:
  odomtopics: ["/odom", /jackal_velocity_controller/odom]
  imtopics: [
    "/spot/camera/frontleft/image/compressed",
    "/spot/camera/frontright/image/compressed",
    "/left/image_color/compressed",
    "/image_raw/compressed",
    "/camera/depth/image_raw/compressed",
    "/image_raw/compressed",
    "/camera/rgb/image_raw/compressed",
    "/spot/camera/left/image/compressed",
    "/spot/camera/right/image/compressed",
    "/spot/camera/back/image/compressed"
  ]
  ang_offset: 0.0
  img_process_func: "process_scand_img"
  odom_process_func: "nav_to_xy_yaw"
  pointcloud_topics: ["/velodyne_points"]
  imu_topics: ["/imu/data_raw"]
  gps_topics: ["/navsat/fix"]
  lidar_topics: ["/scan", "/velodyne_2dscan"]
  joint_states_topics: ["/joint_states"]
  joystick_topics: ["/joystick", "/bluetooth_teleop/joy"]
  cmd_vel_topics: ["/navigation/cmd_vel"]
  tf_topics: ["/tf", "/tf_static"]
  gnss_extra_topics: ["/navsat/time_reference", "/navsat/nmea_sentence", "/navsat/vel"]
  camera_info_topics: [
    "/spot/camera/back/camera_info",
    "/spot/camera/frontleft/camera_info",
    "/spot/camera/frontright/camera_info",
    "/spot/camera/left/camera_info",
    "/spot/camera/right/camera_info"
  ]

locobot:
  odomtopics: "/odom"
  imtopics: "/usb_cam/image_raw"
  ang_offset: 0.0
  img_process_func: "process_locobot_img"
  odom_process_func: "nav_to_xy_yaw"

sacson:
  odomtopics: "/odometry"
  imtopics: "/fisheye_image/compressed"
  ang_offset: 0.0
  img_process_func: "process_sacson_img"
  odom_process_func: "nav_to_xy_yaw"

# add your own datasets below:

# … your existing datasets above …

alcazar:
  ### Dataset is recorded at 10HZ 
  # Odometry comes from the ROS topic /pose
  odomtopics:
    - "/pose"
  # Main RGB camera: use the left fisheye as your frontal view
  imtopics:
    - "/bumblenode/left/image_raw"
  # If you wanted full stereo, you could list right here, too:
  #  - "/bumblenode/right/image_raw"
  # Pull the intrinsic matrix out of the matching camera_info topic
  camera_info_topics:
    - "/bumblenode/left/camera_info"
  #  - "/bumblenode/right/camera_info"
  # No extra yaw offset
  ang_offset: 0.0
  # Re-use the Tartan “raw→numpy→PIL” converter
  img_process_func: "process_tartan_img"
  # Default nav_msgs/Odometry → [x,y],yaw
  odom_process_func: "nav_to_xy_yaw"

gnd:
  # Global Navigation Dataset (GND)
  # - 6 GB bag, 2½ min long. front-facing RGB camera + filtered odometry
  # the topic in your bags
  imtopics:
    - "/camera"
    - "/camera_processed"
    - "zed_node/rgb/image_rect_color/compressed"
  odomtopics:
    - "/odometry/filtered"
  ang_offset: 0.0
  img_process_func: "process_gnd_any"
  odom_process_func: "nav_to_xy_yaw"
  camera_info_topics:
    - "/camera_info"
    - "zed_node/rgb/camera_info"


darpa:
  # color camera (front-facing)
  imtopics:
    - "/camera/color/image_raw/compressed"
    - "/camera_0/image_raw/compressed"
  # use the filtered odometry coming from the depth-color pipeline
  odomtopics:
    - "/camera/depth/color/odom"
  ang_offset: 0.0
  img_process_func: "process_scand_img"       # generic compressed→PIL loader
  odom_process_func: "nav_to_xy_yaw"
  camera_info_topics:
    - "/camera/color/camera_info"
    - "/husky_velocity_controller/odom"

robonav:
  odomtopics:
    - "/ukf_pose"              # Clearpath & Mattro EKF
    - "/robin/vision_odometry" # Spot VIO
    - "/robin/odometry"        # Spot body odom
    - "/navsat/odom"           # EKF with GNSS
    - "/gnss/odom"             # raw GNSS
    - "/odom"                  # wheel encoders
  imtopics:
    - "/hazard_front/zed_node_front/left_raw/image_raw_color"
  camera_info_topics:
    - "/hazard_front/zed_node_front/left_raw/camera_info"
  ang_offset: 0.0
  img_process_func: "process_gnd_any"
  odom_process_func: "nav_to_xy_yaw"


# … your existing entries …

rtsgt:
  # Odometry (nav_msgs/Odometry)
  odomtopics:
    - "/warthog_velocity_controller/odom"
  # Main RGB camera(s): pick the topic(s) you recorded your Basler streams on.
  # If you only have one front camera, you can make this a single string instead of a list.
  imtopics:
    - "/cam/front/image_raw/compressed"
  # If your topics are uncompressed (sensor_msgs/Image), just drop “/compressed”
  # If the camera’s frame is rotated relative to your robot’s odom‐frame,
  # you can adjust here (radians).  Leave at 0.0 if no extra yaw.
  ang_offset: 0.0
  # Which converter to run on each Image message (see img_process_*.py)
  img_process_func: "process_scand_img"
  # How to turn an Odometry into [x,y],yaw
  odom_process_func: "nav_to_xy_yaw"
  # (Optional) camera_info topics if you want us to pull intrinsics automatically
  camera_info_topics:
    - "/cam/front/camera_info"